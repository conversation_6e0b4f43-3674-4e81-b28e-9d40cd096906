<?php
$title = "Register - Step 1 | JobSpace";
$bodyClass = "auth-container";
include BASE_PATH . '/resources/views/components/header/auth-header.php';
?>

    <div class="min-h-screen flex items-center justify-center p-4">
        <div class="bg-white rounded-2xl shadow-2xl w-full max-w-md">
            <!-- Header -->
            <div class="p-8 pb-6">
                <div class="text-center mb-6">
                    <div class="text-4xl mb-2">🚀</div>
                    <h1 class="text-2xl font-bold text-gray-800">Join JobSpace</h1>
                    <p class="text-gray-600">Step 1: Personal Information</p>
                </div>

                <!-- Progress Bar -->
                <div class="flex items-center mb-6">
                    <div class="flex-1 bg-blue-500 h-2 rounded-full"></div>
                    <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-bold mx-2">1</div>
                    <div class="flex-1 bg-gray-200 h-2 rounded-full"></div>
                    <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center text-gray-500 text-sm font-bold ml-2">2</div>
                </div>
            </div>

            <!-- Form -->
            <form action="/jobspace/auth/process-step1" method="POST" class="px-8 pb-8" id="registrationForm">
                <input type="hidden" name="csrf_token" value="<?= \App\Core\Security::generateCSRFToken() ?>"">
                <!-- Name Fields -->
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="block text-gray-700 text-sm font-semibold mb-2">First Name *</label>
                        <input type="text" name="first_name" id="first_name" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500 transition"
                               placeholder="John"
                               value="<?= htmlspecialchars($_SESSION['registration_data']['first_name'] ?? '') ?>">
                        <?php if (isset($_SESSION['registration_errors']['first_name'])): ?>
                            <p class="text-red-500 text-xs mt-1"><?= $_SESSION['registration_errors']['first_name'] ?></p>
                        <?php endif; ?>
                    </div>
                    <div>
                        <label class="block text-gray-700 text-sm font-semibold mb-2">Last Name *</label>
                        <input type="text" name="last_name" id="last_name" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500 transition"
                               placeholder="Doe"
                               value="<?= htmlspecialchars($_SESSION['registration_data']['last_name'] ?? '') ?>">
                        <?php if (isset($_SESSION['registration_errors']['last_name'])): ?>
                            <p class="text-red-500 text-xs mt-1"><?= $_SESSION['registration_errors']['last_name'] ?></p>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Email -->
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-semibold mb-2">Email Address *</label>
                    <input type="email" name="email" id="email" required
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500 transition"
                           placeholder="<EMAIL>"
                           value="<?= htmlspecialchars($_SESSION['registration_data']['email'] ?? '') ?>">
                    <div id="email-feedback" class="text-xs mt-1"></div>
                    <?php if (isset($_SESSION['registration_errors']['email'])): ?>
                        <p class="text-red-500 text-xs mt-1"><?= $_SESSION['registration_errors']['email'] ?></p>
                    <?php endif; ?>
                </div>

                <!-- Phone -->
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-semibold mb-2">Phone Number *</label>
                    <input type="tel" name="phone" id="phone" required
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500 transition"
                           placeholder="+****************"
                           value="<?= htmlspecialchars($_SESSION['registration_data']['phone'] ?? '') ?>">
                    <?php if (isset($_SESSION['registration_errors']['phone'])): ?>
                        <p class="text-red-500 text-xs mt-1"><?= $_SESSION['registration_errors']['phone'] ?></p>
                    <?php endif; ?>
                </div>

                <!-- Date of Birth and Gender -->
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="block text-gray-700 text-sm font-semibold mb-2">Date of Birth *</label>
                        <input type="date" name="date_of_birth" id="date_of_birth" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500 transition custom-datepicker"
                               value="<?= htmlspecialchars($_SESSION['registration_data']['date_of_birth'] ?? '') ?>">
                        <?php if (isset($_SESSION['registration_errors']['date_of_birth'])): ?>
                            <p class="text-red-500 text-xs mt-1"><?= $_SESSION['registration_errors']['date_of_birth'] ?></p>
                        <?php endif; ?>
                    </div>
                    <div>
                        <label class="block text-gray-700 text-sm font-semibold mb-2">Gender *</label>
                        <select name="gender" id="gender" required
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500 transition">
                            <option value="">Select Gender</option>
                            <option value="Male" <?= ($_SESSION['registration_data']['gender'] ?? '') === 'Male' ? 'selected' : '' ?>>Male</option>
                            <option value="Female" <?= ($_SESSION['registration_data']['gender'] ?? '') === 'Female' ? 'selected' : '' ?>>Female</option>
                            <option value="Other" <?= ($_SESSION['registration_data']['gender'] ?? '') === 'Other' ? 'selected' : '' ?>>Other</option>
                        </select>
                        <?php if (isset($_SESSION['registration_errors']['gender'])): ?>
                            <p class="text-red-500 text-xs mt-1"><?= $_SESSION['registration_errors']['gender'] ?></p>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Password -->
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-semibold mb-2">Password *</label>
                    <input type="password" name="password" id="password" required
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500 transition"
                           placeholder="Enter a strong password">
                    <div class="text-xs text-gray-500 mt-1">
                        Must be at least 8 characters with uppercase, lowercase, and numbers
                    </div>
                    <?php if (isset($_SESSION['registration_errors']['password'])): ?>
                        <p class="text-red-500 text-xs mt-1"><?= $_SESSION['registration_errors']['password'] ?></p>
                    <?php endif; ?>
                </div>

                <!-- Confirm Password -->
                <div class="mb-6">
                    <label class="block text-gray-700 text-sm font-semibold mb-2">Confirm Password *</label>
                    <input type="password" name="confirm_password" id="confirm_password" required
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500 transition"
                           placeholder="Confirm your password">
                    <div id="password-match" class="text-xs mt-1"></div>
                    <?php if (isset($_SESSION['registration_errors']['confirm_password'])): ?>
                        <p class="text-red-500 text-xs mt-1"><?= $_SESSION['registration_errors']['confirm_password'] ?></p>
                    <?php endif; ?>
                </div>

                <!-- Submit Button -->
                <button type="submit" 
                        class="w-full bg-blue-500 text-white py-3 rounded-lg font-semibold hover:bg-blue-600 transition duration-200">
                    Continue to Step 2 →
                </button>

                <!-- Login Link -->
                <div class="text-center mt-6">
                    <p class="text-gray-600">Already have an account? 
                        <a href="/jobspace/auth/login" class="text-blue-500 hover:text-blue-600 font-semibold">Login here</a>
                    </p>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Email availability check
        let emailTimeout;
        document.getElementById('email').addEventListener('input', function() {
            clearTimeout(emailTimeout);
            const email = this.value;
            const feedback = document.getElementById('email-feedback');
            
            if (email.length > 0 && email.includes('@')) {
                emailTimeout = setTimeout(() => {
                    fetch(`/jobspace/auth/check-email?email=${encodeURIComponent(email)}`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.available) {
                                feedback.className = 'text-green-500 text-xs mt-1';
                                feedback.textContent = '✓ ' + data.message;
                            } else {
                                feedback.className = 'text-red-500 text-xs mt-1';
                                feedback.textContent = '✗ ' + data.message;
                            }
                        });
                }, 500);
            } else {
                feedback.textContent = '';
            }
        });

        // Password confirmation check
        function checkPasswordMatch() {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            const feedback = document.getElementById('password-match');
            
            if (confirmPassword.length > 0) {
                if (password === confirmPassword) {
                    feedback.className = 'text-green-500 text-xs mt-1';
                    feedback.textContent = '✓ Passwords match';
                } else {
                    feedback.className = 'text-red-500 text-xs mt-1';
                    feedback.textContent = '✗ Passwords do not match';
                }
            } else {
                feedback.textContent = '';
            }
        }

        document.getElementById('password').addEventListener('input', checkPasswordMatch);
        document.getElementById('confirm_password').addEventListener('input', checkPasswordMatch);
    </script>

<?php
// Clear session errors after displaying
unset($_SESSION['registration_errors'], $_SESSION['registration_data']);

include BASE_PATH . '/resources/views/components/footer/auth-footer.php';
?>
