<?php

namespace App\Modules\Auth\Services;

use App\Modules\Auth\Models\User;
use App\Core\Security;
use App\Core\SessionManager;

class AuthService
{
    private array $config;
    
    public function __construct()
    {
        $this->config = include BASE_PATH . '/app/modules/auth/config/auth.php';
    }
    
    /**
     * Validate registration step 1
     */
    public function validateStep1(array $data): array
    {
        $errors = [];
        
        // First Name validation
        if (empty($data['first_name'])) {
            $errors['first_name'] = 'First name is required';
        } elseif (strlen($data['first_name']) > 50) {
            $errors['first_name'] = 'First name must be less than 50 characters';
        }
        
        // Last Name validation
        if (empty($data['last_name'])) {
            $errors['last_name'] = 'Last name is required';
        } elseif (strlen($data['last_name']) > 50) {
            $errors['last_name'] = 'Last name must be less than 50 characters';
        }
        
        // Email validation
        if (empty($data['email'])) {
            $errors['email'] = 'Email is required';
        } elseif (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $errors['email'] = 'Please enter a valid email address';
        } elseif (!User::isEmailAvailable($data['email'])) {
            $errors['email'] = 'This email is already registered';
        }
        
        // Phone validation
        if (empty($data['phone'])) {
            $errors['phone'] = 'Phone number is required';
        } elseif (!preg_match('/^[0-9+\-\s()]+$/', $data['phone'])) {
            $errors['phone'] = 'Please enter a valid phone number';
        }
        
        // Date of birth validation
        if (empty($data['date_of_birth'])) {
            $errors['date_of_birth'] = 'Date of birth is required';
        } else {
            $dob = new \DateTime($data['date_of_birth']);
            $now = new \DateTime();
            $age = $now->diff($dob)->y;
            if ($age < 13) {
                $errors['date_of_birth'] = 'You must be at least 13 years old';
            }
        }
        
        // Gender validation
        if (empty($data['gender'])) {
            $errors['gender'] = 'Please select your gender';
        } elseif (!in_array($data['gender'], ['Male', 'Female', 'Other'])) {
            $errors['gender'] = 'Please select a valid gender';
        }
        
        // Password validation
        if (empty($data['password'])) {
            $errors['password'] = 'Password is required';
        } else {
            $passwordErrors = $this->validatePassword($data['password']);
            if (!empty($passwordErrors)) {
                $errors['password'] = implode(', ', $passwordErrors);
            }
        }
        
        // Confirm password validation
        if (empty($data['confirm_password'])) {
            $errors['confirm_password'] = 'Please confirm your password';
        } elseif ($data['password'] !== $data['confirm_password']) {
            $errors['confirm_password'] = 'Passwords do not match';
        }
        
        return $errors;
    }
    
    /**
     * Validate registration step 2
     */
    public function validateStep2(array $data): array
    {
        $errors = [];
        
        // Username validation
        if (empty($data['username'])) {
            $errors['username'] = 'Username is required';
        } elseif (strlen($data['username']) < 3) {
            $errors['username'] = 'Username must be at least 3 characters';
        } elseif (strlen($data['username']) > 30) {
            $errors['username'] = 'Username must be less than 30 characters';
        } elseif (!preg_match('/^[a-zA-Z0-9_]+$/', $data['username'])) {
            $errors['username'] = 'Username can only contain letters, numbers, and underscores';
        } elseif (!User::isUsernameAvailable($data['username'])) {
            $errors['username'] = 'This username is already taken';
        }
        
        // Address validation (optional)
        if (!empty($data['address']) && strlen($data['address']) > 500) {
            $errors['address'] = 'Address must be less than 500 characters';
        }
        
        // Role validation
        $availableRoles = User::getAvailableRoles();
        if (empty($data['role'])) {
            $errors['role'] = 'Please select a role';
        } elseif (!in_array($data['role'], $availableRoles)) {
            $errors['role'] = 'Please select a valid role';
        }
        
        // Profile picture validation (optional)
        if (!empty($_FILES['profile_picture']['name'])) {
            $fileError = $this->validateProfilePicture($_FILES['profile_picture']);
            if ($fileError) {
                $errors['profile_picture'] = $fileError;
            }
        }
        
        // Referral code validation (optional)
        if (!empty($data['referral_code']) && !User::isValidReferralCode($data['referral_code'])) {
            $errors['referral_code'] = 'Invalid referral code';
        }
        
        // Terms acceptance validation
        if (empty($data['terms_accepted'])) {
            $errors['terms_accepted'] = 'You must accept the terms and conditions';
        }
        
        return $errors;
    }
    
    /**
     * Validate password strength
     */
    private function validatePassword(string $password): array
    {
        $errors = [];
        $config = $this->config['password'];
        
        if (strlen($password) < $config['min_length']) {
            $errors[] = "Password must be at least {$config['min_length']} characters";
        }
        
        if ($config['require_uppercase'] && !preg_match('/[A-Z]/', $password)) {
            $errors[] = 'Password must contain at least one uppercase letter';
        }
        
        if ($config['require_lowercase'] && !preg_match('/[a-z]/', $password)) {
            $errors[] = 'Password must contain at least one lowercase letter';
        }
        
        if ($config['require_numbers'] && !preg_match('/[0-9]/', $password)) {
            $errors[] = 'Password must contain at least one number';
        }
        
        if ($config['require_symbols'] && !preg_match('/[^a-zA-Z0-9]/', $password)) {
            $errors[] = 'Password must contain at least one special character';
        }
        
        return $errors;
    }
    
    /**
     * Validate profile picture upload
     */
    private function validateProfilePicture(array $file): ?string
    {
        $config = $this->config['uploads']['profile_picture'];
        
        if ($file['error'] !== UPLOAD_ERR_OK) {
            return 'File upload failed';
        }
        
        if ($file['size'] > $config['max_size'] * 1024) {
            return 'File size must be less than ' . ($config['max_size'] / 1024) . 'MB';
        }
        
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($extension, $config['allowed_types'])) {
            return 'Only ' . implode(', ', $config['allowed_types']) . ' files are allowed';
        }
        
        return null;
    }
    
    /**
     * Process registration
     */
    public function processRegistration(array $step1Data, array $step2Data): array
    {
        // Combine data
        $userData = array_merge($step1Data, $step2Data);

        // Handle profile picture upload
        if (!empty($_FILES['profile_picture']['name'])) {
            $uploadResult = $this->handleProfilePictureUpload($_FILES['profile_picture']);
            if ($uploadResult['success']) {
                $userData['profile_picture'] = $uploadResult['path'];
            }
        }

        // Hash password
        $userData['password'] = User::hashPassword($userData['password']);
        unset($userData['confirm_password']);

        // Generate verification token and OTP
        $verificationToken = User::generateVerificationToken();
        $otp = User::generateOTP();

        // Generate referral code
        $userData['referral_code_generated'] = User::generateReferralCode($userData['username']);

        // Store verification data
        $verificationData = [
            'user_data' => $userData,
            'verification_token' => $verificationToken,
            'otp' => $otp,
            'otp_expires' => time() + $this->config['otp']['expiry'],
            'created_at' => time()
        ];

        User::storeVerificationData($userData['email'], $verificationData);

        return [
            'success' => true,
            'email' => $userData['email'],
            'verification_token' => $verificationToken,
            'otp' => $otp // In production, don't return OTP
        ];
    }

    /**
     * Handle profile picture upload
     */
    private function handleProfilePictureUpload(array $file): array
    {
        $config = $this->config['uploads']['profile_picture'];

        // Create upload directory if it doesn't exist
        $uploadDir = BASE_PATH . '/public/' . $config['path'];
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        // Generate unique filename
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        $filename = uniqid('profile_') . '.' . $extension;
        $uploadPath = $uploadDir . $filename;
        $relativePath = $config['path'] . $filename;

        // Move uploaded file
        if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
            return [
                'success' => true,
                'path' => $relativePath
            ];
        }

        return [
            'success' => false,
            'error' => 'Failed to upload file'
        ];
    }
    
    /**
     * Verify OTP
     */
    public function verifyOTP(string $email, string $otp): array
    {
        $verificationData = User::getVerificationData($email);

        if (!$verificationData) {
            return ['success' => false, 'message' => 'No verification data found. Please register again.'];
        }

        if (time() > $verificationData['otp_expires']) {
            return ['success' => false, 'message' => 'OTP has expired. Please request a new one.'];
        }

        if ($verificationData['otp'] !== $otp) {
            return ['success' => false, 'message' => 'Invalid OTP. Please check and try again.'];
        }

        // OTP verified successfully, now create the user
        $userData = $verificationData['user_data'];

        // Set verification status
        $userData['email_verified'] = true;
        $userData['email_verified_at'] = date('Y-m-d H:i:s');
        $userData['status'] = 'active';

        // Create user in database
        $userCreated = User::create($userData);

        if (!$userCreated) {
            return ['success' => false, 'message' => 'Failed to create user account. Please try again.'];
        }

        // Clean up verification data
        User::cleanupVerificationData($email);

        return [
            'success' => true,
            'user' => $userData,
            'message' => 'Account created successfully'
        ];
    }
}
